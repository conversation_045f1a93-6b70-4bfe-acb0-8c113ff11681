name: verl_unit_test

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  push:
    branches:
      - main
      - v0.*
  pull_request:
    branches:
      - main
      - v0.*
    paths:
      - "**/*.py"
      - .github/workflows/verl_unit_test.yml
      - "!recipe/**"

# Cancel jobs on the same ref if a new one is triggered
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

# Declare permissions just read content.
permissions:
  contents: read

jobs:
  verl_unit_test:
    runs-on: ubuntu-latest
    timeout-minutes: 10 # Increase this timeout value as needed  
    strategy:
      matrix:
        python-version: ["3.10"]
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@0b93645e9fea7318ecaed2b359559ac225c90a2b # v5.3.0
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install the current repository
        run: |
          pip install -e .[test]
      - name: Running test protocol.py 
        run: |
          cd tests/verl
          pytest -s -x test_protocol.py
      - name: Running utils tests 
        run: |
          cd tests/verl/utils
          pytest -s -x --ignore=dataset/ .
