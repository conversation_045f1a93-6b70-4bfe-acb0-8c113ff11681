name: ray_cpu

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  push:
    branches:
      - main
      - v0.*
  pull_request:
    branches:
      - main
      - v0.*
    paths:
      - "verl/single_controller/*.py"
      - .github/workflows/ray_cpu_test.yml
      - "!recipe/**"

# Cancel jobs on the same ref if a new one is triggered
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

# Declare permissions just read content.
permissions:
  contents: read

jobs:
  ray_cpu:
    runs-on: ubuntu-latest
    timeout-minutes: 10 # Increase this timeout value as needed 
    strategy:
      matrix:
        python-version: ["3.10"]
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@0b93645e9fea7318ecaed2b359559ac225c90a2b # v5.3.0
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install the current repository
        run: |
          pip install -e .[test]
          pip install --upgrade "ray>=2.40.0"
      - name: Running ray tests that can be tested on CPU machines
        run: |
          cd tests/ray_cpu
          pytest -s -x --ignore=test_check_worker_alive.py .
