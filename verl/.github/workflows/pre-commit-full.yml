name: pre-commit-full

# Run weekly on Sunday at 00:00 UTC
on:
  schedule:
    - cron: "0 0 * * 0"
  # Allow manual triggering
  workflow_dispatch:

# Declare permissions just read content.
permissions:
  contents: read

jobs:
  pre-commit-full:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@0b93645e9fea7318ecaed2b359559ac225c90a2b # v5.3.0
        with:
          python-version: ${{ matrix.python-version }}
      - name: Set ruff --output-format=github
        run: |
          sed -i 's/--output-format=full/--output-format=github/' .pre-commit-config.yaml
          git add .pre-commit-config.yaml
      - uses: pre-commit/action@v3.0.1
