name: dataset

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  push:
    branches:
      - main
      - v0.*
  pull_request:
    branches:
      - main
    paths:
      - "verl/utils/**/*.py"
      - .github/workflows/dataset.yml
      - "!verl/workers/fsdp_workers.py"
      - "!verl/workers/megatron_workers.py"
      - "!recipe/**"

# Cancel jobs on the same ref if a new one is triggered
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

# Declare permissions just read content.
permissions:
  contents: read

jobs:
  ray:
    runs-on: [L20x8]
    timeout-minutes: 10 # Increase this timeout value as needed
    env:
      HTTP_PROXY: ${{ secrets.PROXY_HTTP }}
      HTTPS_PROXY: ${{ secrets.PROXY_HTTPS }}
      NO_PROXY: "localhost,127.0.0.1,hf-mirror.com"
      HF_ENDPOINT: "https://hf-mirror.com"
      HF_HUB_ENABLE_HF_TRANSFER: "0" # This is more stable
    container:
      image: whatcanyousee/verl:ngc-cu124-vllm0.8.3-sglang0.4.5-mcore0.12.0-te2.2
      options: --gpus all --shm-size=10g
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Install the current repository
        run: |
          pip install -e .[test]
          pip install --upgrade "ray>=2.40.0"
          pip install cupy-cuda12x
      - name: Running dataset tests
        run: |
          [ ! -d "$HOME/verl-data" ] && git clone --depth 1 https://github.com/eric-haibin-lin/verl-data ~/verl-data
          python3 examples/data_preprocess/geo3k.py
          pytest -s -x tests/verl/utils/dataset/test_rl_dataset.py
          pytest -s -x tests/verl/utils/dataset/test_sft_dataset.py
          pytest -s -x tests/verl/utils/test_import_utils.py
      #          pytest -s -x tests/verl/utils/dataset/test_rm_dataset.py
      - name: Running ray test using cupy (move it to L20 when dockerfile ready)
        run: |
          cd tests/ray_gpu
          pytest -s -x test_rvdz.py
