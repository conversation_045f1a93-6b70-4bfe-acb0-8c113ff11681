name: e2e_eval_aime24

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  push:
    branches:
      - main
      - v0.*
  pull_request:
    branches:
      - main
    paths:
      - "**/*.py"
      # Home
      - "recipe/r1"
      - "!recipe/r1/README.md"
      # Entrypoints
      - ".github/workflows/e2e_eval_aime24.yml"
      - "tests/e2e/run_r1_distill_qwen_aime24_eval.sh"
      - "verl/trainer/main_generation.py"
      - "verl/trainer/config/generation.yaml"
      - "!examples"
      - "!verl/trainer/main_*.py"
      - "!verl/trainer/fsdp_sft_trainer.py"
      # Other recipes
      - "!recipe"

# Declare permissions just read content.
permissions:
  contents: read

jobs:
  e2e_eval_aime24:
    runs-on: [L20x8]
    timeout-minutes: 40 # Increase this timeout value as needed
    env:
      HTTP_PROXY: ${{ secrets.PROXY_HTTP }}
      HTTPS_PROXY: ${{ secrets.PROXY_HTTPS }}
      NO_PROXY: "localhost,127.0.0.1,hf-mirror.com"
      HF_ENDPOINT: "https://hf-mirror.com"
      HF_HUB_ENABLE_HF_TRANSFER: "0" # This is more stable
    container:
      image: whatcanyousee/verl:ngc-cu124-vllm0.8.3-sglang0.4.5-mcore0.12.0-te2.2
      options: --gpus all --shm-size=10g
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Install the current repository
        run: |
          pip3 install --no-deps -e .[test,gpu,math]
          pip3 install math-verify
      - name: Prepare aime24 dataset
        run: |
          ray stop --force
          python3 recipe/r1/data_process.py --task aime2024
      - name: Running generation and evaluation in AIME 2024
        run: |
          ray stop --force
          bash tests/e2e/run_r1_distill_qwen_aime24_eval.sh
