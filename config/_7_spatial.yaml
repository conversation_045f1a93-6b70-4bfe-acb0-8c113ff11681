defaults:
  - base

model_path: Qwen/Qwen2.5-0.5B-Instruct

trainer:
  experiment_name: ToS-active
  project_name: ToS

actor_rollout_ref:
  rollout:
    max_model_len: 8192
    gpu_memory_utilization: 0.7

agent_proxy:
  max_turn: 10 # 1 for passive, 8 for active
  action_sep: "||"
  max_actions_per_turn: 1 # how many actions can be output at most in a single turn

es_manager:
  format_penalty: -0.1
  train:
    env_groups: 8
    # under the same group, the env config and env seed are ensured to be equal
    group_size: 16
    env_configs:
      tags: ["ActiveRot"]
      n_groups: [8] # If not set, all env names divide nums equally. Under the same group, the env config and env seed (prompt) are equal in each generation
  val:
    env_groups: 8
    group_size: 1 # should be set to 1 because when val temperature is set to 0 and group size > 1, there will be repetitive prompts which leads to same trajectory.
    env_configs:
      tags: ["PassiveRot"]
      n_groups: [8] # TODO: If not set, all env names divide nums equally. Under the same group, the env config and env seed (prompt) are equal in each generation
